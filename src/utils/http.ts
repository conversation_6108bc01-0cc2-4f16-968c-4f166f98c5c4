import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  AxiosHeaders,
} from 'axios'
import { i18n } from '@/locales'

export interface HttpResponse<Data = unknown> {
  code?: number
  msg?: string
  data: Data
}

const BASE_URL = (import.meta.env.VITE_API_BASE_URL as string) || '/'
const TIMEOUT_MS = 15000

function getToken(): string | null {
  try {
    return localStorage.getItem('token')
  } catch {
    return null
  }
}

const httpClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: TIMEOUT_MS,
  withCredentials: false,
})

httpClient.interceptors.request.use((config) => {
  const token = getToken()
  const headers = AxiosHeaders.from(config.headers)
  if (token) headers.set('Authorization', `Bearer ${token}`)
  if (!headers.has('Content-Type')) headers.set('Content-Type', 'application/json')
  config.headers = headers
  return config
})

httpClient.interceptors.response.use(
  (response: AxiosResponse<HttpResponse>) => {
    return response
  },
  (error) => {
    const { t } = i18n.global
    const status = error?.response?.status
    let message = t('http.networkError')
    if (error.code === 'ECONNABORTED') message = t('http.timeout')
    else if (status === 401) message = t('http.unauthorized')
    else if (status === 403) message = t('http.forbidden')
    else if (status === 404) message = t('http.notFound')
    else if (status && status >= 500) message = t('http.serverError')
    return Promise.reject({ ...error, friendlyMessage: message })
  },
)

export async function request<ResponseData = unknown, RequestData = unknown>(
  config: AxiosRequestConfig<RequestData>,
): Promise<ResponseData> {
  const resp = await httpClient.request<
    HttpResponse<ResponseData>,
    AxiosResponse<HttpResponse<ResponseData>, RequestData>,
    RequestData
  >(config)
  // 兼容后端不统一场景：优先取 data.data，否则取 data
  const data = (resp.data?.data as ResponseData) ?? (resp.data as unknown as ResponseData)
  return data
}

export const http = {
  get: async <T = unknown>(url: string, config?: AxiosRequestConfig) =>
    request<T>({ ...config, method: 'GET', url }),
  delete: async <T = unknown>(url: string, config?: AxiosRequestConfig) =>
    request<T>({ ...config, method: 'DELETE', url }),
  post: async <T = unknown, B = unknown>(url: string, body?: B, config?: AxiosRequestConfig<B>) =>
    request<T, B>({ ...config, method: 'POST', url, data: body }),
  put: async <T = unknown, B = unknown>(url: string, body?: B, config?: AxiosRequestConfig<B>) =>
    request<T, B>({ ...config, method: 'PUT', url, data: body }),
  patch: async <T = unknown, B = unknown>(url: string, body?: B, config?: AxiosRequestConfig<B>) =>
    request<T, B>({ ...config, method: 'PATCH', url, data: body }),
}

export default http

@use 'sass:color';

// Element Plus 样式与主题变量（按需 SASS）
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #3b82f6,
    ),
  )
);
@use 'element-plus/theme-chalk/src/index.scss' as *;

:root {
  --app-bg: #f7f8fa;
}

html,
body,
#app {
  height: 100%;
}

body {
  margin: 0;
  background: var(--app-bg);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--el-color-primary);
  text-decoration: none;
}

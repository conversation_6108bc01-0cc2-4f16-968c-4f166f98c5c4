import { createI18n } from 'vue-i18n'
import zh_CN from './messages/zh_CN'
import en_US from './messages/en_US'
import fr_FR from './messages/fr_FR'
import pt_PT from './messages/pt_PT'
import es_ES from './messages/es_ES'

const DEFAULT_LOCALE = 'zh_CN' as const
export const SUPPORTED_LOCALES = ['zh_CN', 'en_US', 'fr_FR', 'pt_PT', 'es_ES'] as const
export type LocaleKey = (typeof SUPPORTED_LOCALES)[number]

function normalizeLocale(input: string | null | undefined): LocaleKey {
  const code = (input || '').toLowerCase()
  if (code.includes('zh')) return 'zh_CN'
  if (code.includes('fr')) return 'fr_FR'
  if (code.includes('pt')) return 'pt_PT'
  if (code.includes('es')) return 'es_ES'
  if (code.includes('en')) return 'en_US'
  return DEFAULT_LOCALE
}

function resolveInitialLocale(): LocaleKey {
  const saved = localStorage.getItem('locale')
  if (saved) return normalizeLocale(saved)
  return normalizeLocale(navigator.language)
}

export const i18n = createI18n({
  legacy: false,
  locale: resolveInitialLocale(),
  messages: { zh_CN, en_US, fr_FR, pt_PT, es_ES },
  fallbackLocale: 'en_US',
  globalInjection: true,
})

export type AppMessageSchema = typeof zh_CN

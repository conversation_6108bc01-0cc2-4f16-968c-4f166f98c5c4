export default {
  app: { name: 'Payment' },
  nav: { home: 'Home', about: 'About', language: 'Language' },
  route: { home: 'Home', about: 'About', notFound: 'Not Found' },
  common: { backHome: 'Back Home' },
  http: {
    networkError: 'Network error, please try again later',
    timeout: 'Request timed out, please check your network',
    unauthorized: 'Session expired, please sign in again',
    forbidden: 'Forbidden',
    notFound: 'Resource not found',
    serverError: 'Server error',
  },
} as const

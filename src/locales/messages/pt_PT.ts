export default {
  app: { name: 'Payment' },
  nav: { home: 'Início', about: '<PERSON><PERSON>', language: 'Idioma' },
  route: { home: 'Início', about: 'Sobre', notFound: 'Não encontrado' },
  common: { backHome: 'Voltar ao início' },
  http: {
    networkError: 'Erro de rede, tente novamente mais tarde',
    timeout: 'Tempo de espera esgotado, verifique a sua ligação',
    unauthorized: 'Sess<PERSON> expirada, inicie sessão novamente',
    forbidden: 'Acesso proibido',
    notFound: 'Recurso não encontrado',
    serverError: 'Erro do servidor',
  },
} as const

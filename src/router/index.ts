import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/home/<USER>'),
    meta: { title: 'route.home' },
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('@/views/about/AboutPage.vue'),
    meta: { title: 'route.about' },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/error/NotFoundPage.vue'),
    meta: { title: 'route.notFound' },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 设置页面标题
router.afterEach((to) => {
  import('@/locales').then(({ i18n }) => {
    const t = i18n.global.t
    const titleKey = (to.meta?.title as string) || 'app.name'
    document.title = `${t(titleKey)} - ${t('app.name')}`
  })
})

export default router

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()
const languageOptions = [
  { label: '中文', value: 'zh_CN' },
  { label: 'English', value: 'en_US' },
  { label: 'Français', value: 'fr_FR' },
  { label: 'Português', value: 'pt_PT' },
  { label: 'Español', value: 'es_ES' },
]
function onChangeLang(val: string) {
  locale.value = val
  localStorage.setItem('locale', val)
}
</script>

<template>
  <el-config-provider :button="{ autoInsertSpace: true }">
    <div class="app-layout">
      <el-container class="app-container">
        <el-header class="app-header">
          <div class="brand">{{ t('app.name') }}</div>
          <el-space>
            <RouterLink to="/">{{ t('nav.home') }}</RouterLink>
            <RouterLink to="/about">{{ t('nav.about') }}</RouterLink>
            <el-select
              class="lang-select"
              :model-value="locale"
              size="small"
              style="width: 120px"
              @update:model-value="onChangeLang"
            >
              <el-option
                v-for="opt in languageOptions"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-space>
        </el-header>
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </div>
  </el-config-provider>
</template>

<style scoped lang="scss">
.app-layout {
  min-height: 100vh;
  background: var(--el-bg-color);
}
.app-container {
  max-width: 1080px;
  margin: 0 auto;
}
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}
.brand {
  font-weight: 700;
  letter-spacing: 0.5px;
}
</style>
